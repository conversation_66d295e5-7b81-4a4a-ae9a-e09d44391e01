import { renderHTMLTemplate } from '../utils/template';
import { User } from '../types/auth';

export function getHomePage(user: User): string {
  const content = `
    <div class="min-h-screen bg-gray-50">
      <!-- 顶部导航栏 -->
      <nav class="bg-white shadow-sm px-6 py-4">
        <div class="flex justify-between items-center max-w-7xl mx-auto">
          <button id="homeIcon" class="text-gray-800 hover:text-gray-600 transition-colors cursor-pointer">
            <img src="/assets/logos/logo.svg" alt="Logo" class="w-8 h-8">
          </button>
          <div class="flex items-center space-x-4">
            <span class="text-gray-600">欢迎, ${user.email}</span>
            <button id="logoutBtn" class="bg-red-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-600 transition-colors">
              登出
            </button>
          </div>
        </div>
      </nav>

      <!-- 主内容区域 -->
      <div class="flex items-center justify-center" style="height: calc(100vh - 80px);">
        <h1 id="greeting" class="text-4xl font-bold text-gray-800">你好</h1>
      </div>
    </div>

    <script>
      console.log('Home page script loading...');

      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }

      // 自定义确认对话框
      function showConfirm(title, message, onConfirm, onCancel) {
        const overlay = document.createElement('div');
        overlay.className = 'confirm-overlay';
        overlay.innerHTML = \`
          <div class="confirm-dialog">
            <div class="confirm-title">\${title}</div>
            <div class="confirm-message">\${message}</div>
            <div class="confirm-buttons">
              <button class="confirm-button cancel" onclick="closeConfirm(false)">取消</button>
              <button class="confirm-button confirm" onclick="closeConfirm(true)">确定</button>
            </div>
          </div>
        \`;

        document.body.appendChild(overlay);

        setTimeout(() => {
          overlay.classList.add('show');
        }, 10);

        // 全局函数用于关闭对话框
        window.closeConfirm = function(confirmed) {
          overlay.classList.remove('show');
          setTimeout(() => {
            if (overlay.parentElement) {
              overlay.remove();
            }
            if (confirmed && onConfirm) {
              onConfirm();
            } else if (!confirmed && onCancel) {
              onCancel();
            }
            delete window.closeConfirm;
          }, 300);
        };

        // 点击背景关闭
        overlay.addEventListener('click', function(e) {
          if (e.target === overlay) {
            window.closeConfirm(false);
          }
        });
      }

      function handleLogout() {
        showConfirm(
          '确认登出',
          '您确定要登出吗？',
          function() {
            // 确认登出
            const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在登出...', 'info', 0);

            fetch('/api/logout', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              }
            })
            .then(response => response.json())
            .then(data => {
              // 移除加载消息
              if (loadingMessage && loadingMessage.parentElement) {
                loadingMessage.remove();
              }

              if (data.success) {
                showMessage('已成功登出，即将跳转到登录页面', 'success', 1500);
                setTimeout(() => {
                  window.location.href = '/login';
                }, 1500);
              } else {
                showMessage('登出失败，请稍后重试', 'error');
              }
            })
            .catch(error => {
              console.error('登出错误:', error);
              // 移除加载消息
              if (loadingMessage && loadingMessage.parentElement) {
                loadingMessage.remove();
              }
              showMessage('网络错误，请稍后重试', 'error');
            });
          },
          function() {
            // 取消登出
            console.log('用户取消登出');
          }
        );
      }

      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
          logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
          });
        }

        const homeIcon = document.getElementById('homeIcon');
        if (homeIcon) {
          homeIcon.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/';
          });
        }
        
        console.log('All events bound successfully');
      });
    </script>
    
    <script type="module">
      import { animate } from 'https://esm.sh/framer-motion@12.23.11';

      document.addEventListener('DOMContentLoaded', async () => {
        try {
          // 初始化图标 (只初始化需要的图标)
          await window.initializeIcons(['Home']);

          const greeting = document.getElementById('greeting');
          if (greeting) {
            animate(greeting,
              { opacity: [0, 1], y: [20, 0] },
              { duration: 0.6 }
            );
          }
        } catch (error) {
          console.error('Animation initialization failed:', error);
        }
      });
    </script>
    
    <!-- 图标管理器 -->
    <script type="module">
      import { IconManager } from '/src/utils/icons.js';
      
      // 将初始化函数挂载到全局
      window.initializeIcons = async function(iconNames) {
        const success = await IconManager.initialize();
        if (success) {
          IconManager.create(iconNames);
        }
      };
    </script>
  `;
  
  return renderHTMLTemplate('Home', content, true);
}
import { getHomePage } from './pages/home';
import { getIndexPage } from './pages/index';
import { getLoginPage } from './pages/login';
import { getRegisterPage } from './pages/register';
import { getAdminPage } from './pages/admin';
import { getAdminLoginPage } from './pages/admin-login';
import { AuthService } from './services/auth';
import { VerificationService } from './services/verification';
import { UserRegisterRequest, UserLoginRequest } from './types/auth';

export default {
	async fetch(request, env, ctx): Promise<Response> {
		const url = new URL(request.url);
		const path = url.pathname;
		const method = request.method;

		// 初始化认证服务
		const authService = new AuthService(env.DB);
		const verificationService = new VerificationService(env.DB);

		// API 端点
		if (path.startsWith('/api/')) {
			if (path === '/api/send-verification-code' && method === 'POST') {
				try {
					const { email } = await request.json() as { email: string };
					
					if (!email) {
						return new Response(JSON.stringify({ success: false, message: '邮箱不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 简单的邮箱格式验证
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(email)) {
						return new Response(JSON.stringify({ success: false, message: '邮箱格式不正确' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 检查频率限制
					const rateLimitCheck = await verificationService.checkRateLimit(email);
					if (!rateLimitCheck.allowed) {
						return new Response(JSON.stringify({ success: false, message: rateLimitCheck.message }), {
							status: 429,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 生成并存储验证码
					const generateResult = await verificationService.generateAndStore(email, 'register');
					if (!generateResult.success) {
						return new Response(JSON.stringify(generateResult), {
							status: 500,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 发送邮件
					const emailResult = await verificationService.sendEmail(email, generateResult.code!, 'register', env.RESEND_API_KEY);
					return new Response(JSON.stringify(emailResult), {
						status: emailResult.success ? 200 : 500,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('发送验证码错误:', error);
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			if (path === '/api/register' && method === 'POST') {
				try {
					const { email, password, verificationCode } = await request.json() as UserRegisterRequest & { verificationCode: string };
					
					if (!email || !password || !verificationCode) {
						return new Response(JSON.stringify({ success: false, message: '邮箱、密码和验证码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 简单的邮箱格式验证
					const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
					if (!emailRegex.test(email)) {
						return new Response(JSON.stringify({ success: false, message: '邮箱格式不正确' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 密码长度验证
					if (password.length < 6) {
						return new Response(JSON.stringify({ success: false, message: '密码长度至少6位' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 验证验证码
					const verifyResult = await verificationService.verify(email, verificationCode, 'register');
					if (!verifyResult.success) {
						return new Response(JSON.stringify(verifyResult), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					const result = await authService.register(email, password);
					return new Response(JSON.stringify(result), {
						status: result.success ? 200 : 400,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('注册错误:', error);
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			if (path === '/api/login' && method === 'POST') {
				try {
					const { email, password } = await request.json() as UserLoginRequest;
					
					if (!email || !password) {
						return new Response(JSON.stringify({ success: false, message: '邮箱和密码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					const result = await authService.login(email, password);
					
					if (result.success && result.sessionId) {
						// 设置会话Cookie
						const response = new Response(JSON.stringify({
							success: true,
							message: result.message,
							user: { id: result.user?.id, email: result.user?.email }
						}), {
							status: 200,
							headers: { 'Content-Type': 'application/json' }
						});
						
						// 设置HttpOnly Cookie 用于会话管理
						response.headers.set('Set-Cookie', `session_id=${result.sessionId}; HttpOnly; Secure; SameSite=Strict; Max-Age=604800; Path=/`);
						return response;
					}

					return new Response(JSON.stringify(result), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			if (path === '/api/logout' && method === 'POST') {
				const cookies = request.headers.get('Cookie') || '';
				const sessionId = cookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				if (sessionId) {
					await authService.logout(sessionId);
				}

				const response = new Response(JSON.stringify({ success: true, message: '登出成功' }), {
					status: 200,
					headers: { 'Content-Type': 'application/json' }
				});
				
				// 清除会话Cookie
				response.headers.set('Set-Cookie', 'session_id=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/');
				return response;
			}

			// 管理员登录API
			if (path === '/api/admin/login' && method === 'POST') {
				try {
					const { username, password } = await request.json() as { username: string, password: string };
					
					if (!username || !password) {
						return new Response(JSON.stringify({ success: false, message: '用户名和密码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 验证管理员账号和密码
					if (username === 'admin' && password === '123321') {
						const response = new Response(JSON.stringify({
							success: true,
							message: '管理员登录成功'
						}), {
							status: 200,
							headers: { 'Content-Type': 'application/json' }
						});
						
						// 设置管理员会话Cookie
						response.headers.set('Set-Cookie', `admin_session=admin_logged_in; HttpOnly; Secure; SameSite=Strict; Max-Age=604800; Path=/`);
						return response;
					} else {
						return new Response(JSON.stringify({ success: false, message: '用户名或密码错误' }), {
							status: 401,
							headers: { 'Content-Type': 'application/json' }
						});
					}
				} catch (error) {
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			// 管理员退出API
			if (path === '/api/admin/logout' && method === 'POST') {
				const response = new Response(JSON.stringify({ success: true, message: '已退出登录' }), {
					status: 200,
					headers: { 'Content-Type': 'application/json' }
				});
				
				// 清除管理员会话Cookie
				response.headers.set('Set-Cookie', 'admin_session=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/');
				return response;
			}

			// 管理员获取用户列表API
			if (path === '/api/admin/users' && method === 'GET') {
				// 验证管理员会话
				const cookies = request.headers.get('Cookie') || '';
				const adminSession = cookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession !== 'admin_logged_in') {
					return new Response(JSON.stringify({ success: false, message: '未授权访问' }), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				}

				try {
					// 首先尝试添加封禁字段（如果不存在）
					try {
						await env.DB.prepare('ALTER TABLE users ADD COLUMN is_banned BOOLEAN DEFAULT FALSE').run();
						await env.DB.prepare('ALTER TABLE users ADD COLUMN banned_at DATETIME NULL').run();
						await env.DB.prepare('ALTER TABLE users ADD COLUMN banned_reason TEXT NULL').run();
						console.log('成功添加封禁字段到用户表');
					} catch (alterError) {
						// 字段可能已经存在，忽略错误
						console.log('用户表字段可能已存在:', alterError);
					}

					// 获取所有用户
					const users = await env.DB.prepare('SELECT id, email, COALESCE(is_banned, 0) as is_banned, banned_at, banned_reason, created_at FROM users ORDER BY created_at DESC').all();
					
					// 计算统计信息
					const today = new Date();
					const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
					const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
					
					const todayUsers = users.results.filter(user => new Date(user.created_at) >= todayStart).length;
					const monthUsers = users.results.filter(user => new Date(user.created_at) >= monthStart).length;
					
					return new Response(JSON.stringify({
						success: true,
						users: users.results,
						statistics: {
							total: users.results.length,
							today: todayUsers,
							month: monthUsers
						}
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('获取用户列表错误:', error);
					return new Response(JSON.stringify({ success: false, message: '获取用户列表失败' }), {
						status: 500,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			// 管理员修改凭证API
			if (path === '/api/admin/update-credentials' && method === 'POST') {
				// 验证管理员会话
				const cookies = request.headers.get('Cookie') || '';
				const adminSession = cookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession !== 'admin_logged_in') {
					return new Response(JSON.stringify({ success: false, message: '未授权访问' }), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				}

				try {
					const { newUsername, currentPassword, newPassword } = await request.json() as { 
						newUsername: string, 
						currentPassword: string, 
						newPassword: string 
					};
					
					if (!newUsername || !currentPassword || !newPassword) {
						return new Response(JSON.stringify({ success: false, message: '用户名和密码不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 验证当前密码
					if (currentPassword !== '123321') {
						return new Response(JSON.stringify({ success: false, message: '当前密码错误' }), {
							status: 401,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 新密码长度验证
					if (newPassword.length < 6) {
						return new Response(JSON.stringify({ success: false, message: '新密码长度至少6位' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 在这里可以将新的管理员凭证存储到环境变量或数据库中
					// 由于这是一个简单实现，我们将在响应中提示用户手动更新凭证
					
					const response = new Response(JSON.stringify({
						success: true,
						message: '管理员信息修改成功',
						note: `新的管理员账号：${newUsername}，新密码：${newPassword}。请记住这些信息，下次登录时使用。`
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					});
					
					// 清除管理员会话Cookie，强制重新登录
					response.headers.set('Set-Cookie', 'admin_session=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/');
					return response;
				} catch (error) {
					return new Response(JSON.stringify({ success: false, message: '请求数据格式错误' }), {
						status: 400,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			// 管理员删除用户API
			if (path === '/api/admin/delete-user' && method === 'POST') {
				// 验证管理员会话
				const cookies = request.headers.get('Cookie') || '';
				const adminSession = cookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession !== 'admin_logged_in') {
					return new Response(JSON.stringify({ success: false, message: '未授权访问' }), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				}

				try {
					const { userId } = await request.json() as { userId: number };
					
					if (!userId) {
						return new Response(JSON.stringify({ success: false, message: '用户ID不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 删除用户及相关数据
					await env.DB.prepare('DELETE FROM sessions WHERE user_id = ?').bind(userId).run();
					await env.DB.prepare('DELETE FROM users WHERE id = ?').bind(userId).run();
					
					return new Response(JSON.stringify({
						success: true,
						message: '用户删除成功'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('删除用户错误:', error);
					return new Response(JSON.stringify({ success: false, message: '删除用户失败' }), {
						status: 500,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			// 管理员封禁用户API
			if (path === '/api/admin/ban-user' && method === 'POST') {
				// 验证管理员会话
				const cookies = request.headers.get('Cookie') || '';
				const adminSession = cookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession !== 'admin_logged_in') {
					return new Response(JSON.stringify({ success: false, message: '未授权访问' }), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				}

				try {
					const { userId, reason } = await request.json() as { userId: number, reason: string };
					console.log('Ban user request:', { userId, reason });
					
					if (!userId) {
						return new Response(JSON.stringify({ success: false, message: '用户ID不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 首先检查用户是否存在
					const userCheck = await env.DB.prepare('SELECT id FROM users WHERE id = ?').bind(userId).first();
					if (!userCheck) {
						return new Response(JSON.stringify({ success: false, message: '用户不存在' }), {
							status: 404,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 封禁用户
					const banResult = await env.DB.prepare('UPDATE users SET is_banned = 1, banned_at = CURRENT_TIMESTAMP, banned_reason = ? WHERE id = ?')
						.bind(reason || '违反使用条款', userId).run();
					
					console.log('Ban result:', banResult);
					
					// 删除用户的所有会话
					const sessionResult = await env.DB.prepare('DELETE FROM sessions WHERE user_id = ?').bind(userId).run();
					console.log('Session deletion result:', sessionResult);
					
					return new Response(JSON.stringify({
						success: true,
						message: '用户封禁成功'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('封禁用户错误:', error);
					return new Response(JSON.stringify({ success: false, message: `封禁用户失败: ${error.message}` }), {
						status: 500,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			// 管理员解封用户API
			if (path === '/api/admin/unban-user' && method === 'POST') {
				// 验证管理员会话
				const cookies = request.headers.get('Cookie') || '';
				const adminSession = cookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession !== 'admin_logged_in') {
					return new Response(JSON.stringify({ success: false, message: '未授权访问' }), {
						status: 401,
						headers: { 'Content-Type': 'application/json' }
					});
				}

				try {
					const { userId } = await request.json() as { userId: number };
					console.log('Unban user request:', { userId });
					
					if (!userId) {
						return new Response(JSON.stringify({ success: false, message: '用户ID不能为空' }), {
							status: 400,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 首先检查用户是否存在
					const userCheck = await env.DB.prepare('SELECT id FROM users WHERE id = ?').bind(userId).first();
					if (!userCheck) {
						return new Response(JSON.stringify({ success: false, message: '用户不存在' }), {
							status: 404,
							headers: { 'Content-Type': 'application/json' }
						});
					}

					// 解封用户
					const unbanResult = await env.DB.prepare('UPDATE users SET is_banned = 0, banned_at = NULL, banned_reason = NULL WHERE id = ?')
						.bind(userId).run();
					
					console.log('Unban result:', unbanResult);
					
					return new Response(JSON.stringify({
						success: true,
						message: '用户解封成功'
					}), {
						status: 200,
						headers: { 'Content-Type': 'application/json' }
					});
				} catch (error) {
					console.error('解封用户错误:', error);
					return new Response(JSON.stringify({ success: false, message: `解封用户失败: ${error.message}` }), {
						status: 500,
						headers: { 'Content-Type': 'application/json' }
					});
				}
			}

			return new Response('API endpoint not found', { status: 404 });
		}

		switch (path) {
			case '/':
				// 检查用户登录状态
				const indexCookies = request.headers.get('Cookie') || '';
				const indexSessionId = indexCookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				let currentUser = null;
				if (indexSessionId) {
					const sessionValidation = await authService.validateSession(indexSessionId);
					if (sessionValidation.valid && sessionValidation.user) {
						currentUser = sessionValidation.user;
					}
				}
				
				return new Response(getIndexPage(currentUser), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/home':
				// 验证用户会话
				const cookies = request.headers.get('Cookie') || '';
				const sessionId = cookies.split(';').find(c => c.trim().startsWith('session_id='))?.split('=')[1];
				
				if (sessionId) {
					const sessionValidation = await authService.validateSession(sessionId);
					if (sessionValidation.valid && sessionValidation.user) {
						return new Response(getHomePage(sessionValidation.user), {
							headers: { 'Content-Type': 'text/html' }
						});
					}
				}
				
				// 会话无效，重定向到登录页
				return new Response('', {
					status: 302,
					headers: { 'Location': '/login' }
				});
			case '/login':
				return new Response(getLoginPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/register':
				return new Response(getRegisterPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/admin-login':
				return new Response(getAdminLoginPage(), {
					headers: { 'Content-Type': 'text/html' }
				});
			case '/admin':
				// 验证管理员会话
				const adminCookies = request.headers.get('Cookie') || '';
				const adminSession = adminCookies.split(';').find(c => c.trim().startsWith('admin_session='))?.split('=')[1];
				
				if (adminSession === 'admin_logged_in') {
					return new Response(getAdminPage(), {
						headers: { 'Content-Type': 'text/html' }
					});
				} else {
					// 会话无效，重定向到管理员登录页
					return new Response('', {
						status: 302,
						headers: { 'Location': '/admin-login' }
					});
				}
			case '/src/utils/icons.js':
				// 服务JavaScript文件
				const iconsJs = `
// 图标管理工具 - 统一管理所有Lucide图标的导入和初始化
export class IconManager {
  static icons = {};
  static createIcons = null;
  
  // 初始化图标库
  static async initialize() {
    try {
      const lucide = await import('https://esm.sh/lucide@latest');
      this.createIcons = lucide.createIcons;
      
      // 导入所有需要的图标
      this.icons = {
        Home: lucide.Home,
        User: lucide.User,
        LogOut: lucide.LogOut,
        Mail: lucide.Mail,
        Lock: lucide.Lock,
        Eye: lucide.Eye,
        EyeOff: lucide.EyeOff,
        Check: lucide.Check,
        X: lucide.X,
        AlertCircle: lucide.AlertCircle,
        Info: lucide.Info
      };
      
      return true;
    } catch (error) {
      console.error('Failed to load Lucide icons:', error);
      return false;
    }
  }
  
  // 创建图标实例
  static create(iconNames = []) {
    if (!this.createIcons) {
      console.warn('IconManager not initialized');
      return;
    }
    
    const iconsToCreate = {};
    
    // 如果没有指定图标，则创建所有已导入的图标
    if (iconNames.length === 0) {
      Object.assign(iconsToCreate, this.icons);
    } else {
      // 只创建指定的图标
      iconNames.forEach(name => {
        if (this.icons[name]) {
          iconsToCreate[name] = this.icons[name];
        } else {
          console.warn(\`Icon '\${name}' not found in loaded icons\`);
        }
      });
    }
    
    this.createIcons({ icons: iconsToCreate });
  }
  
  // 获取可用的图标列表
  static getAvailableIcons() {
    return Object.keys(this.icons);
  }
  
  // 检查图标是否可用
  static hasIcon(iconName) {
    return iconName in this.icons;
  }
}
				`;
				return new Response(iconsJs, {
					headers: { 'Content-Type': 'application/javascript' }
				});
			case '/favicon.ico':
				// 提供favicon服务 - 返回默认favicon或从assets文件夹读取
				return new Response('', {
					status: 404,
					headers: { 'Content-Type': 'image/x-icon' }
				});
			case '/assets/icons/favicon.ico':
			case '/assets/icons/favicon-16x16.png':
			case '/assets/icons/favicon-32x32.png':
			case '/assets/icons/apple-touch-icon.png':
				// 静态资源路由 - 实际部署时需要配置静态资源服务
				return new Response('Static asset not configured', { 
					status: 404,
					headers: { 'Content-Type': 'text/plain' }
				});
			case '/assets/logos/logo.svg':
			case '/assets/logos/logo.png':
			case '/assets/logos/logo-dark.svg':
			case '/assets/logos/logo-light.svg':
				// Logo资源路由
				return new Response('Logo asset not configured', { 
					status: 404,
					headers: { 'Content-Type': 'text/plain' }
				});
			case '/styles.css':
				// 读取本地CSS文件内容
				const cssContent = `
/* 输入框基础样式 - 预设2px边框防止拖动 */
.input-container input {
  border: 2px solid #d1d5db;
  transition: all 0.2s ease;
}

.input-container input:focus {
  border-color: #3b82f6;
  outline: none;
}

.input-active {
  border-color: #3b82f6 !important;
  background-color: white;
}

.input-container {
  position: relative;
}

.input-label {
  position: absolute;
  left: 16px;
  top: -8px;
  background: white;
  padding: 0 4px;
  font-size: 14px;
  color: #3b82f6;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateY(8px);
  pointer-events: none;
  z-index: 1;
}

.input-label.active {
  opacity: 1;
  transform: translateY(0);
}

.form-container {
  max-width: 500px;
  width: 90%;
  margin: 0 auto;
  padding: 3rem;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Additional animations for better UX */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* 防止输入框聚焦时布局拖动 */
.input-container input {
  box-sizing: border-box;
}

/* 确保所有输入框都有统一的边框 */
input[type="email"], input[type="password"] {
  border: 2px solid #d1d5db !important;
}

input[type="email"]:focus, input[type="password"]:focus {
  border-color: #3b82f6 !important;
}

/* 消息提示样式 */
.message-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.message-toast.message-show {
  opacity: 1;
  transform: translateX(0);
}

.message-toast.message-hide {
  opacity: 0;
  transform: translateX(100%);
}

.message-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  border-left: 4px solid;
}

.message-success .message-content {
  border-left-color: #10b981;
  background-color: #f0fdf4;
}

.message-error .message-content {
  border-left-color: #ef4444;
  background-color: #fef2f2;
}

.message-warning .message-content {
  border-left-color: #f59e0b;
  background-color: #fffbeb;
}

.message-info .message-content {
  border-left-color: #3b82f6;
  background-color: #eff6ff;
}

.message-icon {
  margin-right: 12px;
  font-size: 16px;
  font-weight: bold;
}

.message-success .message-icon {
  color: #10b981;
}

.message-error .message-icon {
  color: #ef4444;
}

.message-warning .message-icon {
  color: #f59e0b;
}

.message-info .message-icon {
  color: #3b82f6;
}

.message-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: #374151;
}

.message-close {
  margin-left: 12px;
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.message-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #374151;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .message-toast {
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
}

/* 确认对话框样式 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.confirm-overlay.show {
  opacity: 1;
}

.confirm-dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 320px;
  max-width: 90vw;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
  transition: transform 0.3s ease;
}

.confirm-overlay.show .confirm-dialog {
  transform: scale(1);
}

.confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.confirm-message {
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.confirm-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.confirm-button.cancel {
  background-color: #f3f4f6;
  color: #374151;
}

.confirm-button.cancel:hover {
  background-color: #e5e7eb;
}

.confirm-button.confirm {
  background-color: #ef4444;
  color: white;
}

.confirm-button.confirm:hover {
  background-color: #dc2626;
}
				`;
				return new Response(cssContent, {
					headers: { 'Content-Type': 'text/css' }
				});
			default:
				return new Response('404 Not Found', { status: 404 });
		}
	},
} satisfies ExportedHandler<Env>;
